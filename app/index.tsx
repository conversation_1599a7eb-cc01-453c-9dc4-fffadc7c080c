import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, StyleSheet, ActivityIndicator, ScrollView, TouchableOpacity, StatusBar, TextInput, Image } from 'react-native';
import { getVodList } from '../src/api/api'; // Adjusted path
import { VodItem, HomeScreenProps, RootStackParamList } from '../src/types'; // Adjusted path and imported RootStackParamList
import MovieCard from '../src/components/MovieCard'; // Adjusted path
import { Ionicons } from '@expo/vector-icons'; // For icons
import { useRouter } from 'expo-router'; // Import useRouter

const MOVIE_TYPE_ID = 1;
const TV_SHOW_TYPE_ID = 2;
const ANIME_TYPE_ID = 3; // Assuming Anime type ID
const SHORT_DRAMA_TYPE_ID = 4; // Assuming Short Drama type ID
const VARIETY_SHOW_TYPE_ID = 5; // Assuming Variety Show type ID

const cardMargin = 8; // Defined here for use in listContent padding

const HomeScreen: React.FC<HomeScreenProps> = () => {
  const router = useRouter(); // Get router instance
  const [latestMovies, setLatestMovies] = useState<VodItem[]>([]);
  const [latestTVShows, setLatestTVShows] = useState<VodItem[]>([]);
  const [latestAnime, setLatestAnime] = useState<VodItem[]>([]); // New state for Anime
  const [loadingMovies, setLoadingMovies] = useState(true);
  const [loadingTVShows, setLoadingTVShows] = useState(true);
  const [loadingAnime, setLoadingAnime] = useState(true); // New loading state for Anime
  const [errorMovies, setErrorMovies] = useState<string | null>(null);
  const [errorTVShows, setErrorTVShows] = useState<string | null>(null);
  const [errorAnime, setErrorAnime] = useState<string | null>(null); // New error state for Anime
  const [searchQuery, setSearchQuery] = useState('');
  const [featuredVod, setFeaturedVod] = useState<VodItem | null>(null); // State for featured video

  const daysOfWeek = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  const currentDayIndex = new Date().getDay(); // 0 for Sunday, 1 for Monday, ..., 6 for Saturday
  const initialSelectedDay = daysOfWeek[currentDayIndex === 0 ? 6 : currentDayIndex - 1]; // Adjust for Sunday being 0

  const [selectedDay, setSelectedDay] = useState<string>(initialSelectedDay);
  const [weeklyScheduleData, setWeeklyScheduleData] = useState<VodItem[]>([]);
  const [loadingWeeklySchedule, setLoadingWeeklySchedule] = useState(true);
  const [errorWeeklySchedule, setErrorWeeklySchedule] = useState<string | null>(null);

  useEffect(() => {
    const fetchInitialData = async () => {
      // Fetch featured video (e.g., a specific ID or latest popular one)
      try {
        const response = await getVodList({ pagesize: 1 }); // Fetch one item for featured
        if (response.list.length > 0) {
          setFeaturedVod(response.list[0]);
        }
      } catch (e) {
        console.error('Failed to load featured video:', e);
      }

      const fetchMovies = async () => {
        try {
          setLoadingMovies(true);
          setErrorMovies(null);
          const response = await getVodList({ t: MOVIE_TYPE_ID, pg: 1 });
          setLatestMovies(response.list.slice(0, 6));
        } catch (e) {
          setErrorMovies('Failed to load movies.');
        } finally {
          setLoadingMovies(false);
        }
      };

      const fetchTVShows = async () => {
        try {
          setLoadingTVShows(true);
          setErrorTVShows(null);
          const response = await getVodList({ t: TV_SHOW_TYPE_ID, pg: 1 });
          setLatestTVShows(response.list.slice(0, 6));
        } catch (e) {
          setErrorTVShows('Failed to load TV shows.');
        } finally {
          setLoadingTVShows(false);
        }
      };

      const fetchAnime = async () => {
        try {
          setLoadingAnime(true);
          setErrorAnime(null);
          const response = await getVodList({ t: ANIME_TYPE_ID, pg: 1 });
          setLatestAnime(response.list.slice(0, 6));
        } catch (e) {
          setErrorAnime('Failed to load anime.');
        } finally {
          setLoadingAnime(false);
        }
      };

      fetchMovies();
      fetchTVShows();
      fetchAnime(); // Fetch anime data
    };

    const fetchWeeklySchedule = async (day: string) => {
      setLoadingWeeklySchedule(true);
      setErrorWeeklySchedule(null);
      try {
        // This is a placeholder. The API doesn't directly support filtering by day of week.
        // In a real scenario, you might fetch recent updates and filter them by day,
        // or the API might have a specific endpoint for weekly schedules.
        // For now, we'll just fetch some general VOD list data.
        const response = await getVodList({ pg: 1, pagesize: 6 });
        setWeeklyScheduleData(response.list.slice(0, 6)); // Use a subset for display
      } catch (e) {
        setErrorWeeklySchedule('Failed to load weekly schedule.');
      } finally {
        setLoadingWeeklySchedule(false);
      }
    };

    fetchInitialData();
    fetchWeeklySchedule(initialSelectedDay); // Fetch initial weekly schedule
  }, [initialSelectedDay]); // Removed navigation from dependencies

  const handleDaySelect = (day: string) => {
    setSelectedDay(day);
    // In a real app, you would refetch data based on the selected day
    // For now, we'll just re-use the existing weekly schedule data or refetch general data
    // fetchWeeklySchedule(day); // Uncomment this if you implement actual day-based fetching
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push({ pathname: '/Search', params: { query: searchQuery.trim() } } as any);
    }
  };

  const renderMovieCard = ({ item }: { item: VodItem }) => (
    <MovieCard
      item={item}
      onPress={() => router.push({ pathname: '/Detail', params: { vod_id: item.vod_id } } as any)}
    />
  );

  const ListSection: React.FC<{
    title: string;
    data: VodItem[];
    loading: boolean;
    error: string | null;
    onViewMore?: () => void;
  }> = ({ title, data, loading, error, onViewMore }) => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        {onViewMore && <TouchableOpacity onPress={onViewMore}><Text style={styles.viewMore}>更多</Text></TouchableOpacity>}
      </View>
      {loading && <ActivityIndicator size="large" color="#e50914" style={styles.loader} />}
      {error && <Text style={styles.errorText}>{error}</Text>}
      {!loading && !error && data.length === 0 && <Text style={styles.emptyText}>暂无内容。</Text>}
      {!loading && !error && data.length > 0 && (
        <FlatList
          data={data}
          renderItem={renderMovieCard}
          keyExtractor={(item) => item.vod_id.toString()}
          numColumns={2}
          columnWrapperStyle={styles.row}
          contentContainerStyle={styles.listContent}
          scrollEnabled={false} // Disable FlatList scrolling inside ScrollView
        />
      )}
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />

      {/* Custom Header */}
      <View style={styles.header}>
        <View style={styles.navBar}>
          <TouchableOpacity style={styles.navItem} onPress={() => router.push('/' as any)}>
            <Text style={styles.navTextActive}>首页</Text>
            <View style={styles.activeIndicator} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => router.push({ pathname: '/MovieList', params: { title: '电影', typeId: MOVIE_TYPE_ID } } as any)}>
            <Text style={styles.navText}>电影</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => router.push({ pathname: '/TVShowList', params: { title: '电视剧', typeId: TV_SHOW_TYPE_ID } } as any)}>
            <Text style={styles.navText}>电视剧</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => router.push({ pathname: '/MovieList', params: { title: '动漫', typeId: ANIME_TYPE_ID } } as any)}>
            <Text style={styles.navText}>动漫</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => router.push({ pathname: '/MovieList', params: { title: '短剧', typeId: SHORT_DRAMA_TYPE_ID } } as any)}>
            <Text style={styles.navText}>短剧</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => router.push({ pathname: '/MovieList', params: { title: '综艺', typeId: VARIETY_SHOW_TYPE_ID } } as any)}>
            <Text style={styles.navText}>综艺</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => { /* Navigate to Watch History/Library */ }}>
            <Text style={styles.navText}>追剧库</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem}>
            <Ionicons name="time-outline" size={24} color="#f5f5f5" />
          </TouchableOpacity>
        </View>
        <View style={styles.logoContainer}>
          <Text style={styles.logoR}>R</Text>
          <Text style={styles.logoText}>兔子影视</Text>
        </View>
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="搜索电影、电视剧、综艺、动漫"
            placeholderTextColor="#888"
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
          />
          <TouchableOpacity onPress={handleSearch} style={styles.searchIcon}>
            <Ionicons name="search" size={24} color="#f5f5f5" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Featured Video Section */}
      {featuredVod && (
        <TouchableOpacity style={styles.featuredContainer} onPress={() => router.push({ pathname: '/Detail', params: { vod_id: featuredVod.vod_id } } as any)}>
          <Image source={{ uri: featuredVod.vod_pic }} style={styles.featuredImage} />
          <View style={styles.featuredOverlay}>
            <Text style={styles.featuredTitle}>{featuredVod.vod_name}</Text>
            <Text style={styles.featuredEpisode}>{featuredVod.vod_remarks || '暂无简介'}</Text>
            {/* <Text style={styles.featuredDescription}>暂无简介</Text> */}
          </View>
        </TouchableOpacity>
      )}

      {/* Weekly Schedule Section */}
      <View style={styles.weeklyScheduleContainer}>
        <Text style={styles.weeklyScheduleTitle}>追剧周表</Text>
        <View style={styles.weeklyScheduleDays}>
          {daysOfWeek.map((day, index) => (
            <TouchableOpacity
              key={day}
              style={[styles.dayButton, selectedDay === day && styles.activeDayButton]}
              onPress={() => handleDaySelect(day)}
            >
              <Text style={[styles.dayText, selectedDay === day && styles.activeDayText]}>{day}</Text>
            </TouchableOpacity>
          ))}
        </View>
        <ListSection
          title="" // No title needed here, as "追剧周表" is already the title
          data={weeklyScheduleData}
          loading={loadingWeeklySchedule}
          error={errorWeeklySchedule}
          // No onViewMore for weekly schedule for now
        />
      </View>

      <ListSection
        title="最新电影"
        data={latestMovies}
        loading={loadingMovies}
        error={errorMovies}
        onViewMore={() => router.push({ pathname: '/MovieList', params: { title: '最新电影', typeId: MOVIE_TYPE_ID } } as any)}
      />
      <ListSection
        title="最新电视剧"
        data={latestTVShows}
        loading={loadingTVShows}
        error={errorTVShows}
        onViewMore={() => router.push({ pathname: '/TVShowList', params: { title: '最新电视剧', typeId: TV_SHOW_TYPE_ID } } as any)}
      />
      <ListSection
        title="最新动漫"
        data={latestAnime}
        loading={loadingAnime}
        error={errorAnime}
        onViewMore={() => router.push({ pathname: '/MovieList', params: { title: '最新动漫', typeId: ANIME_TYPE_ID } } as any)}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212', // Dark background for the whole screen
  },
  header: {
    backgroundColor: '#1c1c1c',
    paddingTop: StatusBar.currentHeight || 40, // Adjust for notch/status bar
    paddingBottom: 10,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 10,
    marginBottom: 10,
  },
  navItem: {
    paddingVertical: 5,
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  navText: {
    color: '#f5f5f5',
    fontSize: 16,
    fontWeight: 'bold',
  },
  navTextActive: {
    color: '#f5f5f5',
    fontSize: 16,
    fontWeight: 'bold',
  },
  activeIndicator: {
    height: 3,
    width: '80%',
    backgroundColor: '#e50914', // Red indicator
    marginTop: 4,
    borderRadius: 2,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  logoR: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#e50914', // Red 'R'
    marginRight: 5,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#333',
    borderRadius: 25,
    marginHorizontal: 16,
    paddingHorizontal: 15,
    height: 50,
  },
  searchInput: {
    flex: 1,
    color: '#f5f5f5',
    fontSize: 16,
  },
  searchIcon: {
    marginLeft: 10,
  },
  featuredContainer: {
    width: '100%',
    height: 200, // Adjust height as needed
    marginBottom: 20,
    position: 'relative',
  },
  featuredImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  featuredOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    padding: 15,
  },
  featuredTitle: {
    color: '#f5f5f5',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  featuredEpisode: {
    color: '#a0a0a0',
    fontSize: 16,
  },
  featuredDescription: {
    color: '#a0a0a0',
    fontSize: 14,
  },
  weeklyScheduleContainer: {
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  weeklyScheduleTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f5f5f5',
    marginBottom: 10,
  },
  weeklyScheduleDays: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#1c1c1c',
    borderRadius: 10,
    padding: 5,
  },
  dayButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    borderRadius: 8,
  },
  activeDayButton: {
    backgroundColor: '#e50914', // Red for active day
  },
  dayText: {
    color: '#f5f5f5',
    fontSize: 15,
    fontWeight: 'bold',
  },
  activeDayText: {
    color: '#fff',
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f5f5f5',
  },
  viewMore: {
    fontSize: 14,
    color: '#e50914', // Red link color
  },
  loader: {
    marginTop: 20,
    alignSelf: 'center',
  },
  errorText: {
    color: '#ff6b6b',
    textAlign: 'center',
    marginTop: 10,
    paddingHorizontal: 16,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 10,
    color: '#a0a0a0',
    paddingHorizontal: 16,
  },
  row: {
    justifyContent: 'space-around', // Distribute items evenly
    paddingHorizontal: cardMargin / 2,
  },
  listContent: {
    paddingHorizontal: cardMargin / 2,
  }
});

export default HomeScreen;
