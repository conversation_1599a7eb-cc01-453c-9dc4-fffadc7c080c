import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, StyleSheet, ActivityIndicator, ScrollView, TouchableOpacity, StatusBar, TextInput, Image } from 'react-native';
import { getVodList } from '../api/api';
import { VodItem, HomeScreenProps } from '../types';
import MovieCard from '../components/MovieCard';
import ApiTest from '../components/ApiTest';
import { Ionicons } from '@expo/vector-icons'; // For icons

const MOVIE_TYPE_ID = 1;
const TV_SHOW_TYPE_ID = 2;
const ANIME_TYPE_ID = 3; // Assuming Anime type ID
const SHORT_DRAMA_TYPE_ID = 4; // Assuming Short Drama type ID
const VARIETY_SHOW_TYPE_ID = 5; // Assuming Variety Show type ID

const cardMargin = 8; // Defined here for use in listContent padding

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const [latestMovies, setLatestMovies] = useState<VodItem[]>([]);
  const [latestTVShows, setLatestTVShows] = useState<VodItem[]>([]);
  const [latestAnime, setLatestAnime] = useState<VodItem[]>([]); // New state for Anime
  const [loadingMovies, setLoadingMovies] = useState(true);
  const [loadingTVShows, setLoadingTVShows] = useState(true);
  const [loadingAnime, setLoadingAnime] = useState(true); // New loading state for Anime
  const [errorMovies, setErrorMovies] = useState<string | null>(null);
  const [errorTVShows, setErrorTVShows] = useState<string | null>(null);
  const [errorAnime, setErrorAnime] = useState<string | null>(null); // New error state for Anime
  const [searchQuery, setSearchQuery] = useState('');
  const [featuredVod, setFeaturedVod] = useState<VodItem | null>(null); // State for featured video

  const daysOfWeek = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  const currentDayIndex = new Date().getDay(); // 0 for Sunday, 1 for Monday, ..., 6 for Saturday
  const initialSelectedDay = daysOfWeek[currentDayIndex === 0 ? 6 : currentDayIndex - 1]; // Adjust for Sunday being 0

  const [selectedDay, setSelectedDay] = useState<string>(initialSelectedDay);
  const [weeklyScheduleData, setWeeklyScheduleData] = useState<VodItem[]>([]);
  const [loadingWeeklySchedule, setLoadingWeeklySchedule] = useState(true);
  const [errorWeeklySchedule, setErrorWeeklySchedule] = useState<string | null>(null);

  useEffect(() => {
    // Hide the default header provided by AppNavigator
    navigation.setOptions({
      headerShown: false,
    });

    const fetchInitialData = async () => {
      // Fetch featured video (e.g., a specific ID or latest popular one)
      try {
        const response = await getVodList({ pg: 1 }); // Fetch first page for featured
        if (response.list.length > 0) {
          setFeaturedVod(response.list[0]);
        }
      } catch (e) {
        console.error('Failed to load featured video:', e);
      }

      const fetchMovies = async () => {
        try {
          setLoadingMovies(true);
          setErrorMovies(null);
          const response = await getVodList({ t: MOVIE_TYPE_ID, pg: 1 });
          setLatestMovies(response.list.slice(0, 6));
        } catch (e) {
          console.error('Failed to load movies:', e);
          setErrorMovies('无法加载电影数据');
        } finally {
          setLoadingMovies(false);
        }
      };

      const fetchTVShows = async () => {
        try {
          setLoadingTVShows(true);
          setErrorTVShows(null);
          const response = await getVodList({ t: TV_SHOW_TYPE_ID, pg: 1 });
          setLatestTVShows(response.list.slice(0, 6));
        } catch (e) {
          console.error('Failed to load TV shows:', e);
          setErrorTVShows('无法加载电视剧数据');
        } finally {
          setLoadingTVShows(false);
        }
      };

      const fetchAnime = async () => {
        try {
          setLoadingAnime(true);
          setErrorAnime(null);
          const response = await getVodList({ t: ANIME_TYPE_ID, pg: 1 });
          setLatestAnime(response.list.slice(0, 6));
        } catch (e) {
          console.error('Failed to load anime:', e);
          setErrorAnime('无法加载动漫数据');
        } finally {
          setLoadingAnime(false);
        }
      };

      // Execute all fetches
      await Promise.all([fetchMovies(), fetchTVShows(), fetchAnime()]);
    };

    const fetchWeeklySchedule = async () => {
      setLoadingWeeklySchedule(true);
      setErrorWeeklySchedule(null);
      try {
        // This is a placeholder. The API doesn't directly support filtering by day of week.
        // In a real scenario, you might fetch recent updates and filter them by day,
        // or the API might have a specific endpoint for weekly schedules.
        // For now, we'll just fetch some general VOD list data.
        const response = await getVodList({ pg: 1 });
        setWeeklyScheduleData(response.list.slice(0, 6)); // Use a subset for display
      } catch (e) {
        console.error('Failed to load weekly schedule:', e);
        setErrorWeeklySchedule('无法加载追剧周表');
      } finally {
        setLoadingWeeklySchedule(false);
      }
    };

    fetchInitialData();
    fetchWeeklySchedule(); // Fetch initial weekly schedule
  }, [navigation]); // Fixed dependencies

  const handleDaySelect = (day: string) => {
    setSelectedDay(day);
    // In a real app, you would refetch data based on the selected day
    // For now, we'll just re-use the existing weekly schedule data or refetch general data
    // fetchWeeklySchedule(); // Uncomment this if you implement actual day-based fetching
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      navigation.navigate('Search', { query: searchQuery.trim() });
    }
  };

  const renderMovieCard = ({ item }: { item: VodItem }) => (
    <MovieCard
      item={item}
      onPress={() => navigation.navigate('Detail', { vod_id: item.vod_id })}
    />
  );

  const ListSection: React.FC<{
    title: string;
    data: VodItem[];
    loading: boolean;
    error: string | null;
    onViewMore?: () => void;
  }> = ({ title, data, loading, error, onViewMore }) => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        {onViewMore && <TouchableOpacity onPress={onViewMore}><Text style={styles.viewMore}>更多</Text></TouchableOpacity>}
      </View>
      {loading && <ActivityIndicator size="large" color="#e50914" style={styles.loader} />}
      {error && <Text style={styles.errorText}>{error}</Text>}
      {!loading && !error && data.length === 0 && <Text style={styles.emptyText}>暂无内容。</Text>}
      {!loading && !error && data.length > 0 && (
        <FlatList
          data={data}
          renderItem={renderMovieCard}
          keyExtractor={(item) => item.vod_id.toString()}
          horizontal={true} // Enable horizontal scrolling
          showsHorizontalScrollIndicator={false} // Hide scroll indicator
          contentContainerStyle={styles.horizontalListContent} // Use new style for horizontal list
        />
      )}
    </View>
  );

  // Debug info for development
  const isAllLoading = loadingMovies && loadingTVShows && loadingAnime && loadingWeeklySchedule;
  const hasAnyError = errorMovies || errorTVShows || errorAnime || errorWeeklySchedule;
  const hasAnyData = latestMovies.length > 0 || latestTVShows.length > 0 || latestAnime.length > 0 || weeklyScheduleData.length > 0;

  return (
    <ScrollView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />

      {/* Debug Info - Remove in production */}
      {__DEV__ && (
        <View style={styles.debugContainer}>
          <Text style={styles.debugText}>
            Loading: {isAllLoading ? 'Yes' : 'No'} |
            Errors: {hasAnyError ? 'Yes' : 'No'} |
            Data: {hasAnyData ? 'Yes' : 'No'}
          </Text>
          <Text style={styles.debugText}>
            Movies: {latestMovies.length} | TV: {latestTVShows.length} | Anime: {latestAnime.length}
          </Text>
          <ApiTest />
        </View>
      )}

      {/* Custom Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoR}>R</Text>
            <Text style={styles.logoText}>rabbitmv</Text>
          </View>
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="搜索电影、电视剧、综艺、动漫"
              placeholderTextColor="#888"
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
            />
            <TouchableOpacity onPress={handleSearch} style={styles.searchIcon}>
              <Ionicons name="search" size={24} color="#f5f5f5" />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.navBar}>
          <TouchableOpacity style={styles.navItem}>
            <Text style={styles.navTextActive}>首页</Text>
            <View style={styles.activeIndicator} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => navigation.navigate('HomeTabs', { screen: 'Movies', params: { title: '电影', typeId: MOVIE_TYPE_ID } })}>
            <Text style={styles.navText}>电影</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => navigation.navigate('HomeTabs', { screen: 'TVShows', params: { title: '电视剧', typeId: TV_SHOW_TYPE_ID } })}>
            <Text style={styles.navText}>电视剧</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => navigation.navigate('HomeTabs', { screen: 'Movies', params: { title: '动漫', typeId: ANIME_TYPE_ID } })}>
            <Text style={styles.navText}>动漫</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => navigation.navigate('HomeTabs', { screen: 'Movies', params: { title: '短剧', typeId: SHORT_DRAMA_TYPE_ID } })}>
            <Text style={styles.navText}>短剧</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => navigation.navigate('HomeTabs', { screen: 'Movies', params: { title: '综艺', typeId: VARIETY_SHOW_TYPE_ID } })}>
            <Text style={styles.navText}>综艺</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navItem} onPress={() => navigation.navigate('HomeTabs', { screen: 'Profile' })}>
            <Text style={styles.navText}>追剧库</Text>
          </TouchableOpacity>
          {/* Removed the time-outline icon as it's not in the Figma design for the top nav */}
        </View>
      </View>

      {/* Featured Video Section */}
      {featuredVod ? (
        <TouchableOpacity style={styles.featuredContainer} onPress={() => navigation.navigate('Detail', { vod_id: featuredVod.vod_id })}>
          <Image source={{ uri: featuredVod.vod_pic }} style={styles.featuredImage} />
          <View style={styles.featuredOverlay}>
            <Text style={styles.featuredTitle}>{featuredVod.vod_name}</Text>
            <Text style={styles.featuredEpisode}>{featuredVod.vod_remarks || '暂无简介'}</Text>
          </View>
        </TouchableOpacity>
      ) : (
        <View style={styles.featuredPlaceholder}>
          <Text style={styles.placeholderText}>正在加载推荐内容...</Text>
        </View>
      )}

      {/* Weekly Schedule Section */}
      <View style={styles.weeklyScheduleContainer}>
        <Text style={styles.weeklyScheduleTitle}>追剧周表</Text>
        <View style={styles.weeklyScheduleDays}>
          {daysOfWeek.map((day, index) => (
            <TouchableOpacity
              key={day}
              style={[styles.dayButton, selectedDay === day && styles.activeDayButton]}
              onPress={() => handleDaySelect(day)}
            >
              <Text style={[styles.dayText, selectedDay === day && styles.activeDayText]}>{day}</Text>
            </TouchableOpacity>
          ))}
        </View>
        <ListSection
          title="" // No title needed here, as "追剧周表" is already the title
          data={weeklyScheduleData}
          loading={loadingWeeklySchedule}
          error={errorWeeklySchedule}
          // No onViewMore for weekly schedule for now
        />
      </View>

      <ListSection
        title="最新电影"
        data={latestMovies}
        loading={loadingMovies}
        error={errorMovies}
        onViewMore={() => navigation.navigate('HomeTabs', { screen: 'Movies', params: { title: '最新电影', typeId: MOVIE_TYPE_ID } })}
      />
      <ListSection
        title="最新电视剧"
        data={latestTVShows}
        loading={loadingTVShows}
        error={errorTVShows}
        onViewMore={() => navigation.navigate('HomeTabs', { screen: 'TVShows', params: { title: '最新电视剧', typeId: TV_SHOW_TYPE_ID } })}
      />
      <ListSection
        title="最新动漫"
        data={latestAnime}
        loading={loadingAnime}
        error={errorAnime}
        onViewMore={() => navigation.navigate('HomeTabs', { screen: 'Movies', params: { title: '最新动漫', typeId: ANIME_TYPE_ID } })}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A1A1A', // Dark background for the whole screen from Figma
  },
  header: {
    backgroundColor: '#1A1A1A', // Match container background for seamless look
    paddingTop: StatusBar.currentHeight || 40, // Adjust for notch/status bar
    paddingBottom: 10,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  navBar: {
    flexDirection: 'row',
    justifyContent: 'flex-start', // Align items to the start
    alignItems: 'center',
    paddingHorizontal: 16, // Adjusted padding
    marginBottom: 10,
  },
  navItem: {
    paddingVertical: 5,
    paddingHorizontal: 0, // Adjusted padding
    alignItems: 'center',
    marginRight: 15, // Added margin between items
  },
  navText: {
    color: '#f5f5f5',
    fontSize: 16,
    fontWeight: 'bold',
  },
  navTextActive: {
    color: '#f5f5f5',
    fontSize: 16,
    fontWeight: 'bold',
  },
  activeIndicator: {
    height: 3,
    width: '100%', // Adjusted width
    backgroundColor: '#e50914', // Red indicator
    marginTop: 4,
    borderRadius: 2,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // Removed justifyContent: 'center'
    marginBottom: 0, // Adjusted margin
  },
  logoR: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#e50914', // Red 'R'
    marginRight: 5,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#333',
    borderRadius: 25,
    // Removed marginHorizontal
    paddingHorizontal: 15,
    height: 40, // Adjusted height
    width: '60%', // Adjusted width
  },
  searchInput: {
    flex: 1,
    color: '#f5f5f5',
    fontSize: 16,
    paddingVertical: 0, // Ensure text input is centered vertically
  },
  searchIcon: {
    marginLeft: 10,
  },
  featuredContainer: {
    width: '100%',
    height: 250, // Adjust height as needed
    marginBottom: 20,
    position: 'relative',
  },
  featuredImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  featuredOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.8)', // Darker overlay
    padding: 15,
  },
  featuredTitle: {
    color: '#f5f5f5',
    fontSize: 32, // Larger font size
    fontWeight: 'bold',
    marginBottom: 5,
  },
  featuredEpisode: {
    color: '#E0E0E0', // Lighter gray
    fontSize: 18, // Larger font size
  },
  featuredDescription: {
    color: '#a0a0a0',
    fontSize: 14,
  },
  weeklyScheduleContainer: {
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  weeklyScheduleTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f5f5f5',
    marginBottom: 10,
  },
  weeklyScheduleDays: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#1c1c1c',
    borderRadius: 10,
    padding: 5,
  },
  dayButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    borderRadius: 8,
  },
  activeDayButton: {
    backgroundColor: '#e50914', // Red for active day
  },
  dayText: {
    color: '#f5f5f5',
    fontSize: 15,
    fontWeight: 'bold',
  },
  activeDayText: {
    color: '#fff',
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f5f5f5',
  },
  viewMore: {
    fontSize: 14,
    color: '#e50914', // Red link color
  },
  loader: {
    marginTop: 20,
    alignSelf: 'center',
  },
  errorText: {
    color: '#ff6b6b',
    textAlign: 'center',
    marginTop: 10,
    paddingHorizontal: 16,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 10,
    color: '#a0a0a0',
    paddingHorizontal: 16,
  },
  row: {
    justifyContent: 'space-around', // Distribute items evenly
    paddingHorizontal: cardMargin / 2,
  },
  listContent: {
    paddingHorizontal: cardMargin / 2,
  },
  horizontalListContent: {
    paddingHorizontal: cardMargin, // Add padding for horizontal list
  },
  debugContainer: {
    backgroundColor: '#333',
    padding: 10,
    margin: 10,
    borderRadius: 5,
  },
  debugText: {
    color: '#fff',
    fontSize: 12,
    marginBottom: 5,
  },
  featuredPlaceholder: {
    width: '100%',
    height: 250,
    backgroundColor: '#2c2c2c',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  placeholderText: {
    color: '#a0a0a0',
    fontSize: 16,
  },
});

export default HomeScreen;
