import React from 'react';
import { NavigationContainer, NavigatorScreenParams } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'; // Import bottom tabs
import { Ionicons } from '@expo/vector-icons'; // For icons

// Import screen components
import HomeScreen from '../screens/HomeScreen';
import MovieListScreen from '../screens/MovieListScreen';
import TVShowListScreen from '../screens/TVShowListScreen';
import DetailScreen from '../screens/DetailScreen';
import PlayerScreen from '../screens/PlayerScreen';
import SearchScreen from '../screens/SearchScreen';
import ProfileScreen from '../screens/ProfileScreen'; // Import ProfileScreen

// Define the ParamList for the stack navigator
export type RootStackParamList = {
  HomeTabs: NavigatorScreenParams<BottomTabParamList> | undefined;
  Detail: { vod_id: number };
  Player: { videoUrl: string; title?: string };
  Search: { query?: string };
};

// Define the ParamList for the bottom tab navigator
export type BottomTabParamList = {
  Home: undefined;
  Movies: { typeId?: number; title?: string };
  TVShows: { typeId?: number; title?: string };
  Profile: undefined; // New screen for user profile
};

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<BottomTabParamList>(); // Create Tab navigator instance

const MOVIE_TYPE_ID = 1;
const TV_SHOW_TYPE_ID = 2;

// Create a new component for the bottom tabs
const HomeTabs = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Movies') {
            iconName = focused ? 'film' : 'film-outline';
          } else if (route.name === 'TVShows') {
            iconName = focused ? 'tv' : 'tv-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-circle-outline'; // Default icon
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#e50914', // Red for active tab
        tabBarInactiveTintColor: '#ADADAD', // Gray for inactive tab
        tabBarStyle: {
          backgroundColor: '#262626', // Dark background for tab bar
          borderTopWidth: 1,
          borderTopColor: '#363636', // Border color from Figma
          height: 60, // Adjust height as needed
          paddingBottom: 5, // Adjust padding for icons/labels
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: 'bold',
        },
        headerShown: false, // Hide header for tab screens
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} options={{ title: '首页' }} />
      <Tab.Screen name="Movies" component={MovieListScreen} options={{ title: '电影' }} initialParams={{ typeId: MOVIE_TYPE_ID, title: '电影' }} />
      <Tab.Screen name="TVShows" component={TVShowListScreen} options={{ title: '电视剧' }} initialParams={{ typeId: TV_SHOW_TYPE_ID, title: '电视剧' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: '我的' }} /> {/* Placeholder for ProfileScreen */}
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="HomeTabs">
        <Stack.Screen name="HomeTabs" component={HomeTabs} options={{ headerShown: false }} />
        <Stack.Screen name="Detail" component={DetailScreen} options={{ title: 'Details', headerStyle: { backgroundColor: '#1c1c1c' }, headerTintColor: '#fff' }} />
        <Stack.Screen name="Player" component={PlayerScreen} options={{ title: 'Now Playing', headerShown: false }} />
        <Stack.Screen name="Search" component={SearchScreen} options={{ title: 'Search', headerStyle: { backgroundColor: '#1c1c1c' }, headerTintColor: '#fff' }} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
