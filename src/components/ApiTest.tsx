import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { getVodList } from '../api/api';

const ApiTest: React.FC = () => {
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const testApi = async () => {
    setIsLoading(true);
    setTestResult('Testing API...');
    
    try {
      // Test basic API call
      const response = await getVodList({ pg: 1 });
      setTestResult(`Success! Got ${response.list.length} items. First item: ${response.list[0]?.vod_name || 'No items'}`);
    } catch (error) {
      setTestResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testMovieApi = async () => {
    setIsLoading(true);
    setTestResult('Testing Movie API...');
    
    try {
      const response = await getVodList({ t: 1, pg: 1 });
      setTestResult(`Movies: Got ${response.list.length} items. First movie: ${response.list[0]?.vod_name || 'No movies'}`);
    } catch (error) {
      setTestResult(`Movie Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Test</Text>
      
      <TouchableOpacity style={styles.button} onPress={testApi} disabled={isLoading}>
        <Text style={styles.buttonText}>Test Basic API</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={testMovieApi} disabled={isLoading}>
        <Text style={styles.buttonText}>Test Movie API</Text>
      </TouchableOpacity>
      
      <ScrollView style={styles.resultContainer}>
        <Text style={styles.resultText}>{testResult}</Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#1A1A1A',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f5f5f5',
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#e50914',
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonText: {
    color: '#fff',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  resultContainer: {
    backgroundColor: '#2c2c2c',
    padding: 15,
    borderRadius: 5,
    maxHeight: 200,
  },
  resultText: {
    color: '#f5f5f5',
    fontSize: 14,
  },
});

export default ApiTest;
