// src/types/index.ts

// Interface for items in VOD lists (e.g., Home, MovieList, TVShowList, SearchResults)
export interface VodItem {
  vod_id: number;
  vod_name: string;
  vod_pic: string;         // URL for the poster image
  vod_remarks?: string;     // e.g., "HD", "更新至10集"
  vod_time?: string;        // Last update time
  type_name?: string;       // Category, e.g., "动作片"
  vod_year?: string;
  vod_area?: string;
  // Add any other fields that commonly appear in list views
  // For example, if the API returns a score or a brief summary here
}

// Interface for the detailed information of a VOD item
export interface VodDetail extends VodItem {
  vod_director?: string;    // Director(s)
  vod_actor?: string;       // Actor(s)
  vod_content?: string;     // Synopsis/plot details
  vod_play_url: string;    // String with playback URLs, e.g., "第一集$url1#第二集$url2"
  vod_blurb?: string;       // Short summary or tagline
  vod_class?: string;       // Detailed genre classification
  // Include all fields available from the ac=detail API endpoint
}

// Interface for parsed episode/playback link
export interface PlayLink {
  episode: string;
  url: string;
}

// Interface for items in Article lists
export interface ArticleItem {
  art_id: number;
  art_name: string;
  type_id: number;
  type_name: string;
  art_en?: string;
  art_time?: string;
  art_author?: string;
  art_from?: string;
  art_remarks?: string;
  art_pic?: string;
}

// Interface for detailed information of an Article item
export interface ArticleDetail extends ArticleItem {
  art_content?: string;
}

// Interface for items in Actor lists
export interface ActorItem {
  actor_id: number;
  actor_name: string;
  type_id: number;
  type_name: string;
  actor_en?: string;
  actor_alias?: string;
  actor_sex?: string;
  actor_area?: string;
  actor_pic?: string;
  actor_time?: string;
}

// Interface for detailed information of an Actor item
export interface ActorDetail extends ActorItem {
  actor_content?: string;
}

// Interface for items in Role lists
export interface RoleItem {
  role_id: number;
  role_name: string;
  role_en?: string;
  role_actor?: string;
  role_rid?: string;
  role_pic?: string;
  role_time?: string;
  douban_id?: string;
  vod_name?: string;
  vod_director?: string;
}

// Interface for detailed information of a Role item
export interface RoleDetail extends RoleItem {
  role_content?: string;
}

// Define the ParamList for the stack navigator
// This helps with type safety for navigation props
import { CompositeScreenProps } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList, BottomTabParamList } from '../navigation/AppNavigator';

// Helper types for screen props
export type HomeScreenProps = CompositeScreenProps<
  BottomTabScreenProps<BottomTabParamList, 'Home'>,
  NativeStackScreenProps<RootStackParamList>
>;
export type MovieListScreenProps = CompositeScreenProps<
  BottomTabScreenProps<BottomTabParamList, 'Movies'>,
  NativeStackScreenProps<RootStackParamList>
>;
export type TVShowListScreenProps = CompositeScreenProps<
  BottomTabScreenProps<BottomTabParamList, 'TVShows'>,
  NativeStackScreenProps<RootStackParamList>
>;
export type ProfileScreenProps = CompositeScreenProps<
  BottomTabScreenProps<BottomTabParamList, 'Profile'>,
  NativeStackScreenProps<RootStackParamList>
>;
export type DetailScreenProps = NativeStackScreenProps<RootStackParamList, 'Detail'>;
export type PlayerScreenProps = NativeStackScreenProps<RootStackParamList, 'Player'>;
export type SearchScreenProps = NativeStackScreenProps<RootStackParamList, 'Search'>;
